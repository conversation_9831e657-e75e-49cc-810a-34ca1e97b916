# Generated by Django 5.2.4 on 2025-07-18 09:14

import utils.ordering
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0010_alter_brand_slug_alter_category_slug'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='productvariantattributevalue',
            options={'ordering': ['order', 'attribute_value__attribute__title'], 'verbose_name': 'Product Variant Attribute Value'},
        ),
        migrations.AddField(
            model_name='productvariantattributevalue',
            name='order',
            field=utils.ordering.OrderField(default=1, help_text='Display order for this attribute value'),
            preserve_default=False,
        ),
    ]
