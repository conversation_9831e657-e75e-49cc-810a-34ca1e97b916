// Products hooks using TanStack Query
// Provides reactive product data management

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ProductService } from '../services/product-service'
import { useNotifications } from '../stores/ui-store'
import { queryKeys } from '../services/query-keys'
import type {
  Product,
  ProductFilters,
  AttributeValue,
  AttributeValueFormData,
  AttributeValueCreateData,
  AttributeValueBulkCreateData,
  ProductImage,
  ProductImageCreateData,
  ProductCreateWithVariantsData,
  BrandProductType,
  BrandProductTypeBulkAssociate,
  ProductTypeAttribute,
  ProductTypeAttributeAssociation
} from '../types/api-types'

/**
 * Hook for fetching paginated products list
 */
export const useProducts = (filters?: ProductFilters) => {
  return useQuery({
    queryKey: queryKeys.products.list(filters),
    queryFn: () => ProductService.getProducts(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    // placeholderData: (previousData) => previousData,
  })
}

/**
 * Hook for fetching single product
 */
export const useProduct = (id: number) => {
  return useQuery({
    queryKey: queryKeys.products.detail(id),
    queryFn: () => ProductService.getProduct(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for fetching full product details including variants, images, and attribute values
 */
export const useFullProductDetails = (id: number) => {
  return useQuery({
    queryKey: queryKeys.products.fullDetail(id),
    queryFn: () => ProductService.getFullProductDetails(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for creating new product
 */
export const useCreateProduct = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createProduct,
    onSuccess: (newProduct) => {
      showSuccess('Product Created', `${newProduct.name} has been created successfully.`)

      // Invalidate products list
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })

      // Add to cache
      queryClient.setQueryData(
        queryKeys.products.detail(newProduct.id),
        newProduct
      )
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create product.')
    },
  })
}

/**
 * Hook for updating product
 */
export const useUpdateProduct = (id: number) => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: Partial<Product>) => ProductService.updateProduct(id, data),
    onSuccess: (updatedProduct) => {
      showSuccess('Product Updated', `${updatedProduct.name} has been updated successfully.`)

      // Update cached data
      queryClient.setQueryData(
        queryKeys.products.detail(id),
        updatedProduct
      )

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product.')
    },
  })
}

/**
 * Hook for updating product details only
 */
export const useUpdateProductDetails = (id: number) => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: Partial<Product>) => ProductService.updateProduct(id, data),
    onSuccess: (updatedProduct) => {
      showSuccess('Product Details Updated', 'Product details have been updated successfully.')

      // Update cached data
      queryClient.setQueryData(
        queryKeys.products.detail(id),
        updatedProduct
      )
      queryClient.setQueryData(
        queryKeys.products.fullDetail(id),
        updatedProduct
      )

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product details.')
    },
  })
}

/**
 * Hook for updating product variant
 */
export const useUpdateProductVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<ProductVariant> }) =>
      ProductService.updateVariant(id, data),
    onSuccess: (updatedVariant) => {
      showSuccess('Variant Updated', `Variant ${updatedVariant.sku} has been updated successfully.`)

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update variant.')
    },
  })
}

/**
 * Hook for updating product image
 */
export const useUpdateProductImageDetails = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<ProductImage> }) =>
      ProductService.updateProductImage(id, data),
    onSuccess: (updatedImage) => {
      showSuccess('Image Updated', 'Product image has been updated successfully.')

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.images(updatedImage.product_variant) })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update image.')
    },
  })
}

/**
 * Hook for updating variant attribute value association
 */
export const useUpdateVariantAttributeValueDetails = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: { is_active?: boolean } }) =>
      ProductService.updateVariantAttributeValue(id, data),
    onSuccess: () => {
      showSuccess('Attribute Association Updated', 'Attribute value association has been updated successfully.')

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute association.')
    },
  })
}

/**
 * Hook for deleting product
 */
export const useDeleteProduct = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteProduct,
    onSuccess: (_, productId) => {
      showSuccess('Product Deleted', 'Product has been deleted successfully.')

      // Remove from cache
      queryClient.removeQueries({ queryKey: queryKeys.products.detail(productId) })

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete product.')
    },
  })
}

/**
 * Hook for bulk product operations
 */
export const useBulkProductOperation = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.bulkOperation,
    onSuccess: (result, { action, productIds }) => {
      const count = productIds.length
      showSuccess(
        'Bulk Operation Complete',
        `Successfully ${action} ${count} product${count > 1 ? 's' : ''}.`
      )

      // Invalidate all product queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Bulk Operation Failed', error.message || 'Failed to complete bulk operation.')
    },
  })
}

/**
 * Hook for product categories
 */
export const useProductCategories = () => {
  return useQuery({
    queryKey: queryKeys.products.categories(),
    queryFn: ProductService.getCategories,
    staleTime: 15 * 60 * 1000, // 15 minutes - categories don't change often
  })
}

/**
 * Hook for product brands
 */
export const useProductBrands = () => {
  return useQuery({
    queryKey: queryKeys.products.brands(),
    queryFn: ProductService.getBrands,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

/**
 * Hook for product types
 */
export const useProductTypes = () => {
  return useQuery({
    queryKey: queryKeys.products.types(),
    queryFn: ProductService.getProductTypes,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

/**
 * Hook for creating category
 */
export const useCreateCategory = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createCategory,
    onSuccess: (newCategory) => {
      showSuccess('Category Created', `${newCategory.name} has been created successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.categories() })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create category.')
    },
  })
}

/**
 * Hook for creating product type
 */
export const useCreateProductType = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createProductType,
    onSuccess: (newType) => {
      showSuccess('Product Type Created', `${newType.name} has been created successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create product type.')
    },
  })
}

/**
 * Hook for creating brand
 */
export const useCreateBrand = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createBrand,
    onSuccess: (newBrand) => {
      showSuccess('Brand Created', `${newBrand.title} has been created successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create brand.')
    },
  })
}

/**
 * Hook for product variants
 */
export const useProductVariants = (productId: number) => {
  return useQuery({
    queryKey: queryKeys.products.variants(productId),
    queryFn: () => ProductService.getProductVariants(productId),
    enabled: !!productId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for creating variant
 */
export const useCreateVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createVariant,
    onSuccess: (newVariant) => {
      showSuccess('Variant Created', `Variant ${newVariant.sku} has been created successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create variant.')
    },
  })
}

/**
 * Hook for updating variant
 */
export const useUpdateVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<any> }) =>
      ProductService.updateVariant(id, data),
    onSuccess: (updatedVariant) => {
      showSuccess('Variant Updated', `${updatedVariant.sku} has been updated successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update variant.')
    },
  })
}

/**
 * Hook for deleting variant
 */
export const useDeleteVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteVariant,
    onSuccess: () => {
      showSuccess('Variant Deleted', 'Variant has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete variant.')
    },
  })
}

/**
 * Hook for updating category
 */
export const useUpdateCategory = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<any> }) =>
      ProductService.updateCategory(id, data),
    onSuccess: (updatedCategory) => {
      showSuccess('Category Updated', `${updatedCategory.name} has been updated successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.categories() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update category.')
    },
  })
}

/**
 * Hook for deleting category
 */
export const useDeleteCategory = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteCategory,
    onSuccess: () => {
      showSuccess('Category Deleted', 'Category has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.categories() })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete category.')
    },
  })
}

// /**
//  * Hook for creating product
//  */
// export const useCreateProduct = () => {
//   const queryClient = useQueryClient()
//   const { showSuccess, showError } = useNotifications()

//   return useMutation({
//     mutationFn: ProductService.createProduct,
//     onSuccess: (newProduct) => {
//       showSuccess('Product Created', `${newProduct.title} has been created successfully.`)
//       queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
//     },
//     onError: (error: any) => {
//       showError('Creation Failed', error.message || 'Failed to create product.')
//     },
//   })
// }

/**
 * Hook for updating brand
 */
export const useUpdateBrand = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<any> }) =>
      ProductService.updateBrand(id, data),
    onSuccess: (updatedBrand) => {
      showSuccess('Brand Updated', `${updatedBrand.title} has been updated successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update brand.')
    },
  })
}

/**
 * Hook for product attributes
 */
export const useAttributes = () => {
  return useQuery({
    queryKey: queryKeys.products.attributes(),
    queryFn: ProductService.getAttributes,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

/**
 * Hook for creating attribute
 */
export const useCreateAttribute = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createAttribute,
    onSuccess: (newAttribute) => {
      showSuccess('Attribute Created', `${newAttribute.title} has been created successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributes() })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create attribute.')
    },
  })
}

/**
 * Hook for updating attribute
 */
export const useUpdateAttribute = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<any> }) =>
      ProductService.updateAttribute(id, data),
    onSuccess: (updatedAttribute) => {
      showSuccess('Attribute Updated', `${updatedAttribute.title} has been updated successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributes() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute.')
    },
  })
}

/**
 * Hook for deleting attribute
 */
export const useDeleteAttribute = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteAttribute,
    onSuccess: () => {
      showSuccess('Attribute Deleted', 'Attribute has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributes() })
    },
    onError: (error: any) => {
      showError('Delete Failed', error.message || 'Failed to delete attribute.')
    },
  })
}

/**
 * Hook for deleting brand
 */
export const useDeleteBrand = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteBrand,
    onSuccess: () => {
      showSuccess('Brand Deleted', 'Brand has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete brand.')
    },
  })
}

/**
 * Hook for fetching product types associated with a brand
 */
export const useBrandProductTypes = (brandId: number) => {
  return useQuery({
    queryKey: ['brand-product-types', brandId],
    queryFn: () => ProductService.getBrandProductTypes(brandId),
    enabled: !!brandId,
    staleTime: 10 * 60 * 1000,
  })
}

/**
 * Hook for setting product types for a brand
 */
export const useSetBrandProductTypes = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({ brandId, productTypeIds }: { brandId: number; productTypeIds: number[] }) =>
      ProductService.setBrandProductTypes(brandId, productTypeIds),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['brand-product-types', variables.brandId] })
    },
  })
}

/**
 * Hook for deleting product type
 */
export const useDeleteProductType = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteProductType,
    onSuccess: (_, productTypeId) => {
      showSuccess('Product Type Deleted', 'Product type has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: unknown) => {
      if (error && typeof error === 'object' && 'message' in error) {
        // @ts-expect-error: message might exist
        showError('Deletion Failed', error.message || 'Failed to delete product type.')
      } else {
        showError('Deletion Failed', 'Failed to delete product type.')
      }
    },
  })
}

// Brand-Product Type Association Hooks

/**
 * Hook for fetching all brand-product type associations
 */
export const useBrandProductTypeAssociations = () => {
  return useQuery({
    queryKey: queryKeys.products.brandProductTypes(),
    queryFn: ProductService.getBrandProductTypeAssociations,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for bulk associating brands with product types
 */
export const useBulkAssociateBrandProductTypes = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.bulkAssociateBrandProductTypes,
    onSuccess: () => {
      showSuccess('Associations Created', 'Brand-Product Type associations have been created successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brandProductTypes() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: any) => {
      showError('Association Failed', error.message || 'Failed to create brand-product type associations.')
    },
  })
}

/**
 * Hook for deleting brand-product type association
 */
export const useDeleteBrandProductTypeAssociation = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteBrandProductTypeAssociation,
    onSuccess: () => {
      showSuccess('Association Deleted', 'Brand-Product Type association has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brandProductTypes() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete brand-product type association.')
    },
  })
}

// Product Type-Attribute Association Hooks

/**
 * Hook for fetching attributes associated with a product type
 */
export const useProductTypeAttributes = (productTypeId: number) => {
  return useQuery({
    queryKey: queryKeys.products.productTypeAttributes(productTypeId),
    queryFn: () => ProductService.getProductTypeAttributes(productTypeId),
    enabled: !!productTypeId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for associating attributes with a product type
 */
export const useAssociateProductTypeAttributes = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productTypeId, attributes }: {
      productTypeId: number
      attributes: ProductTypeAttributeAssociation[]
    }) => ProductService.associateProductTypeAttributes(productTypeId, attributes),
    onSuccess: (_, variables) => {
      showSuccess('Attributes Associated', 'Attributes have been associated with the product type successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(variables.productTypeId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributes() })
    },
    onError: (error: any) => {
      showError('Association Failed', error.message || 'Failed to associate attributes with product type.')
    },
  })
}

/**
 * Hook for updating product type attribute association
 */
export const useUpdateProductTypeAttribute = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productTypeId, attributeId, data }: {
      productTypeId: number
      attributeId: number
      data: Partial<ProductTypeAttributeAssociation>
    }) => ProductService.updateProductTypeAttribute(productTypeId, attributeId, data),
    onSuccess: (_, variables) => {
      showSuccess('Association Updated', 'Product type attribute association has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(variables.productTypeId) })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product type attribute association.')
    },
  })
}

/**
 * Hook for removing attribute from product type
 */
export const useRemoveProductTypeAttribute = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productTypeId, attributeId }: { productTypeId: number; attributeId: number }) =>
      ProductService.removeProductTypeAttribute(productTypeId, attributeId),
    onSuccess: (_, variables) => {
      showSuccess('Attribute Removed', 'Attribute has been removed from the product type successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(variables.productTypeId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: any) => {
      showError('Removal Failed', error.message || 'Failed to remove attribute from product type.')
    },
  })
}

// Attribute Value Hooks

/**
 * Hook for fetching attribute values
 */
export const useAttributeValues = (attributeId?: number) => {
  return useQuery({
    queryKey: queryKeys.products.attributeValues(attributeId),
    queryFn: () => ProductService.getAttributeValues(attributeId),
    enabled: attributeId ? !!attributeId : true,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for fetching single attribute value
 */
export const useAttributeValue = (id: number) => {
  return useQuery({
    queryKey: queryKeys.products.attributeValue(id),
    queryFn: () => ProductService.getAttributeValue(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for creating attribute value
 */
export const useCreateAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createAttributeValue,
    onSuccess: (newValue) => {
      showSuccess('Value Created', `"${newValue.attribute_value}" has been created successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributeValues(newValue.attribute) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributes() })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create attribute value.')
    },
  })
}

/**
 * Hook for updating attribute value
 */
export const useUpdateAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<AttributeValueFormData> }) =>
      ProductService.updateAttributeValue(id, data),
    onSuccess: (updatedValue) => {
      showSuccess('Value Updated', `"${updatedValue.attribute_value}" has been updated successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributeValues(updatedValue.attribute) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributeValue(updatedValue.id) })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute value.')
    },
  })
}

/**
 * Hook for deleting attribute value
 */
export const useDeleteAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteAttributeValue,
    onSuccess: (_, id) => {
      showSuccess('Value Deleted', 'Attribute value has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete attribute value.')
    },
  })
}

/**
 * Hook for bulk creating attribute values
 */
export const useBulkCreateAttributeValues = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.bulkCreateAttributeValues,
    onSuccess: (createdValues, variables) => {
      showSuccess('Values Created', `${createdValues.length} attribute values have been created successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributeValues(variables.attribute) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributes() })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create attribute values.')
    },
  })
}

// Variant Attribute Value Hooks

/**
 * Hook for fetching variant attribute values
 */
export const useVariantAttributeValues = (variantId: number) => {
  return useQuery({
    queryKey: ['variant-attribute-values', variantId],
    queryFn: () => ProductService.getVariantAttributeValues(variantId),
    enabled: !!variantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for creating variant attribute value association
 */
export const useCreateVariantAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createVariantAttributeValue,
    onSuccess: (_, variables) => {
      showSuccess('Association Created', 'Attribute value has been associated with the variant successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values', variables.product_variant] })
    },
    onError: (error: any) => {
      showError('Association Failed', error.message || 'Failed to associate attribute value with variant.')
    },
  })
}

/**
 * Hook for updating variant attribute value association
 */
export const useUpdateVariantAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: { is_active?: boolean } }) =>
      ProductService.updateVariantAttributeValue(id, data),
    onSuccess: () => {
      showSuccess('Association Updated', 'Attribute value association has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute value association.')
    },
  })
}

/**
 * Hook for deleting variant attribute value association
 */
export const useDeleteVariantAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteVariantAttributeValue,
    onSuccess: () => {
      showSuccess('Association Deleted', 'Attribute value association has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete attribute value association.')
    },
  })
}

/**
 * Hook for bulk associating attribute values with a variant
 */
export const useBulkAssociateVariantAttributeValues = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.bulkAssociateVariantAttributeValues,
    onSuccess: (result, variables) => {
      showSuccess('Bulk Association', `${result.created_count || 0} attribute values have been associated with the variant.`)
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values', variables.product_variant_id] })
    },
    onError: (error: any) => {
      showError('Bulk Association Failed', error.message || 'Failed to associate attribute values with variant.')
    },
  })
}

/**
 * Hook for bulk updating status of variant attribute value associations
 */
export const useBulkUpdateVariantAttributeValueStatus = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.bulkUpdateVariantAttributeValueStatus,
    onSuccess: (result) => {
      showSuccess('Status Updated', `${result.updated_count || 0} associations have been updated.`)
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Status Update Failed', error.message || 'Failed to update association status.')
    },
  })
}

// Product Creation with Variants Hooks

/**
 * Hook for creating product with variants
 */
export const useCreateProductWithVariants = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createProductWithVariants,
    onSuccess: (result) => {
      showSuccess('Product Created', `"${result.product.title}" has been created with ${result.variants.length} variant(s).`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })
      queryClient.setQueryData(
        queryKeys.products.detail(result.product.id),
        result.product
      )
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create product with variants.')
    },
  })
}

// Product Image Hooks

/**
 * Hook for fetching product images
 */
export const useProductImages = (variantId: number) => {
  return useQuery({
    queryKey: queryKeys.products.images(variantId),
    queryFn: () => ProductService.getProductImages(variantId),
    enabled: !!variantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for uploading product image
 */
export const useUploadProductImage = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.uploadProductImage,
    onSuccess: (newImage) => {
      showSuccess('Image Uploaded', 'Product image has been uploaded successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.images(newImage.product_variant) })
    },
    onError: (error: any) => {
      showError('Upload Failed', error.message || 'Failed to upload product image.')
    },
  })
}

/**
 * Hook for updating product image
 */
export const useUpdateProductImage = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<ProductImageCreateData> }) =>
      ProductService.updateProductImage(id, data),
    onSuccess: (updatedImage) => {
      showSuccess('Image Updated', 'Product image has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.images(updatedImage.product_variant) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.image(updatedImage.id) })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product image.')
    },
  })
}

/**
 * Hook for deleting product image
 */
export const useDeleteProductImage = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteProductImage,
    onSuccess: (_, id) => {
      showSuccess('Image Deleted', 'Product image has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete product image.')
    },
  })
}

/**
 * Hook for individual image reorder
 */
export const useReorderProductImage = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ imageId, newOrder }: { imageId: number; newOrder: number }) =>
      ProductService.reorderProductImage(imageId, newOrder),
    onSuccess: () => {
      showSuccess('Image Reordered', 'Product image has been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder product image.')
    },
  })
}

/**
 * Hook for drag-and-drop reordering product images
 */
export const useReorderProductImagesDragDrop = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productVariantId, orderedIds }: { productVariantId: number; orderedIds: number[] }) =>
      ProductService.reorderProductImagesDragDrop(productVariantId, orderedIds),
    onSuccess: () => {
      showSuccess('Images Reordered', 'Product images have been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder product images.')
    },
  })
}

/**
 * Hook for fetching attribute values by product type
 */
export const useAttributeValuesByProductType = (productTypeId: number) => {
  return useQuery({
    queryKey: ['product-type-attribute-values', productTypeId],
    queryFn: () => ProductService.getAttributeValuesByProductType(productTypeId),
    enabled: !!productTypeId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for fetching variant attribute values by variant
 */
export const useVariantAttributeValuesByVariant = (variantId: number) => {
  return useQuery({
    queryKey: ['variant-attribute-values', variantId],
    queryFn: () => ProductService.getVariantAttributeValuesByVariant(variantId),
    enabled: !!variantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for individual variant attribute value reorder
 */
export const useReorderVariantAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ associationId, newOrder }: { associationId: number; newOrder: number }) =>
      ProductService.reorderVariantAttributeValue(associationId, newOrder),
    onSuccess: () => {
      showSuccess('Reordered', 'Attribute value has been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder attribute value.')
    },
  })
}

/**
 * Hook for drag-and-drop reordering variant attribute values
 */
export const useReorderVariantAttributeValuesDragDrop = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productVariantId, orderedIds }: { productVariantId: number; orderedIds: number[] }) =>
      ProductService.reorderVariantAttributeValuesDragDrop(productVariantId, orderedIds),
    onSuccess: () => {
      showSuccess('Reordered', 'Attribute values have been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder attribute values.')
    },
  })
}

/**
 * Hook for bulk updating variant attribute values order
 */
export const useBulkUpdateVariantAttributeValuesOrder = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (orderUpdates: { id: number; order: number }[]) =>
      ProductService.bulkUpdateVariantAttributeValuesOrder(orderUpdates),
    onSuccess: () => {
      showSuccess('Updated', 'Attribute values order has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute values order.')
    },
  })
}

/**
 * Hook for individual product variant reorder
 */
export const useReorderProductVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ variantId, newOrder }: { variantId: number; newOrder: number }) =>
      ProductService.reorderProductVariant(variantId, newOrder),
    onSuccess: () => {
      showSuccess('Reordered', 'Product variant has been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder product variant.')
    },
  })
}

/**
 * Hook for drag-and-drop reordering product variants
 */
export const useReorderProductVariantsDragDrop = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productId, orderedIds }: { productId: number; orderedIds: number[] }) =>
      ProductService.reorderProductVariantsDragDrop(productId, orderedIds),
    onSuccess: () => {
      showSuccess('Reordered', 'Product variants have been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder product variants.')
    },
  })
}

/**
 * Hook for bulk updating product variants order
 */
export const useBulkUpdateProductVariantsOrder = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (orderUpdates: { id: number; order: number }[]) =>
      ProductService.bulkUpdateProductVariantsOrder(orderUpdates),
    onSuccess: () => {
      showSuccess('Updated', 'Product variants order has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product variants order.')
    },
  })
}
